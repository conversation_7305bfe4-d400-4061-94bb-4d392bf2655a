# Reference this view as bigbrain___temp_product__csproddb_public_organisations_organisationsettings
hidden: true

schema: bigbrain
table_name: _temp_product__csproddb_public_organisations_organisationsettings

dimensions:
  uba_enabled: {}
  academy_enabled: {}
  certificate_auto_renewal_enabled: {}
  devices_cleaning_include_mobile: {}
  policies_reminders: {}
  notifications_enabled: {}
  cybersmart_learn_enabled: {}
  cybersmart_learn_enablement: {}
  academy_reminders: {}
  academy_notifications: {}
  patch_enabled: {}
  features_help_text: {}
  lms_send_email_notifications: {}
  smart_score_enabled: {}
  devices_cleaning_days: {}
  devices_cleaning_visible: {}
  cap_steps_to_fix_enabled: {}
  web_notifications_enabled: {}
  default_language: {}
  policies_notifications: {}
  enforce_multi_factor_authentication: {}
  r_n_r_toolbox_enabled: {}
  cap_vulnerable_software_enabled: {}
  cap_auto_update_enabled: {}
  app_checks_notifications: {}
  app_checks_reminders: {}
  devices_cleaning_enabled: {}
  modified: {}
  created: {}

  id:
    format: ID
    primary_key: true

  organization_id:
    format: ID

measures:
  count:
    aggregate_type: count

# Reference this view as bigbrain__segment__javascript_jacques_test
schema: bigbrain
table_name: segment__javascript_jacques_test

dimensions:
  account_type: {}
  context_ip: {}
  context_library_name: {}
  context_library_version: {}
  context_locale: {}
  context_page_path: {}
  context_page_title: {}
  context_page_url: {}
  context_user_agent: {}
  event: {}
  event_text: {}
  loaded_at: {}
  original_timestamp: {}
  plan: {}
  received_at: {}
  sent_at: {}
  timestamp: {}
  user: {}
  uuid_ts: {}

  anonymous_id:
    format: ID

  id:
    format: ID
    primary_key: true

  user_id:
    format: ID

measures:
  count:
    aggregate_type: count

# Reference this view as bigbrain__segment__stripe_invoices
schema: bigbrain
table_name: segment__stripe_invoices

dimensions:
  amount_due: {}
  attempt_count: {}
  attempted: {}
  closed: {}
  currency: {}
  date: {}
  ending_balance: {}
  forgiven: {}
  loaded_at: {}
  paid: {}
  period_end: {}
  period_start: {}
  receipt_number: {}
  received_at: {}
  starting_balance: {}
  subtotal: {}
  tax: {}
  tax_percent: {}
  total: {}
  uuid_ts: {}
  created: {}

  charge_id:
    format: ID

  customer_id:
    format: ID

  discount_id:
    format: ID

  id:
    format: ID
    primary_key: true

  subscription_id:
    format: ID

measures:
  count:
    aggregate_type: count

# Reference this view as bigbrain__segment__sendgrid_processed
schema: bigbrain
table_name: segment__sendgrid_processed

dimensions:
  category: {}
  email: {}
  event: {}
  loaded_at: {}
  received_at: {}
  sg_template_name: {}
  timestamp: {}
  uuid_ts: {}
  send_at: {}
  mc_stats: {}
  audience: {}
  name: {}
  environment: {}

  anymail_id:
    format: ID

  id:
    format: ID
    primary_key: true

  sg_event_id:
    format: ID

  sg_message_id:
    format: ID

  sg_template_id:
    format: ID

  smtp_id:
    format: ID

  sendtest_id:
    format: ID

  template_id:
    format: ID

measures:
  count:
    aggregate_type: count

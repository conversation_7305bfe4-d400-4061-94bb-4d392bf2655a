# Reference this view as bigbrain__segment__aircall_call_agent_declined
schema: bigbrain
table_name: segment__aircall_call_agent_declined

dimensions:
  archived: {}
  comments: {}
  context_library_name: {}
  context_library_version: {}
  direct_link: {}
  direction: {}
  duration: {}
  event: {}
  event_text: {}
  loaded_at: {}
  number_availability_status: {}
  number_country: {}
  number_digits: {}
  number_direct_link: {}
  number_is_ivr: {}
  number_live_recording_activated: {}
  number_messages_callback_later: {}
  number_messages_closed: {}
  number_messages_ivr: {}
  number_messages_voicemail: {}
  number_messages_waiting: {}
  number_messages_welcome: {}
  number_name: {}
  number_open: {}
  number_time_zone: {}
  original_timestamp: {}
  raw_digits: {}
  received_at: {}
  sent_at: {}
  started_at: {}
  status: {}
  tags: {}
  teams: {}
  timestamp: {}
  user_availability_status: {}
  user_available: {}
  user_direct_link: {}
  user_email: {}
  user_name: {}
  uuid_ts: {}
  user_language: {}
  cost: {}
  contact_company_name: {}
  contact_created_at: {}
  contact_direct_link: {}
  contact_emails: {}
  contact_first_name: {}
  contact_is_shared: {}
  contact_phone_numbers: {}
  contact_updated_at: {}
  user_wrap_up_time: {}
  contact_last_name: {}
  ended_at: {}
  missed_call_reason: {}
  call_uuid: {}

  _id:
    format: ID
    hidden: true

  id:
    format: ID
    primary_key: true

  number_id:
    format: ID

  user_id:
    format: ID

  contact_id:
    format: ID

measures:
  count:
    aggregate_type: count

# Reference this view as bigbrain__segment__javascript_plan_purchased
schema: bigbrain
table_name: segment__javascript_plan_purchased

dimensions:
  context_ip: {}
  context_library_name: {}
  context_library_version: {}
  context_locale: {}
  context_page_path: {}
  context_page_title: {}
  context_page_url: {}
  context_user_agent: {}
  currency: {}
  event: {}
  event_text: {}
  loaded_at: {}
  original_timestamp: {}
  received_at: {}
  revenue: {}
  sent_at: {}
  timestamp: {}
  uuid_ts: {}
  context_page_referrer: {}
  items: {}

  anonymous_id:
    format: ID

  id:
    format: ID
    primary_key: true

  user_id:
    format: ID

measures:
  count:
    aggregate_type: count

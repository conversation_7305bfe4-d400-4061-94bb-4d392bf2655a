# Reference this view as bigbrain__segment__intercom_conversation_parts
schema: bigbrain
table_name: segment__intercom_conversation_parts

dimensions:
  assigned_to_type: {}
  author_type: {}
  body: {}
  created_at: {}
  loaded_at: {}
  notified_at: {}
  part_type: {}
  received_at: {}
  updated_at: {}
  uuid_ts: {}

  assigned_to_id:
    format: ID

  author_id:
    format: ID

  conversation_id:
    format: ID

  id:
    format: ID
    primary_key: true

measures:
  count:
    aggregate_type: count

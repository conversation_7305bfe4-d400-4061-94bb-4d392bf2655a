# Reference this view as bigbrain___temp_product__csproddb_public_rulebook_surveyquestion
hidden: true

schema: bigbrain
table_name: _temp_product__csproddb_public_rulebook_surveyquestion

dimensions:
  widget: {}
  code: {}
  text_box_is_required: {}
  tooltip: {}
  response_type: {}
  pervade_ignored: {}
  custom_validators: {}
  title: {}
  auto_answer_survey_question_boolean: {}
  required: {}
  other_form: {}
  common_reasons: {}
  show_text_box: {}
  insurer: {}
  pervade_save_text_to_answer: {}
  considered_as_valid: {}
  pervade_save_applicant_notes: {}
  auto_answer: {}
  order: {}
  max_answers_amount: {}
  show_children_on_2: {}
  other_form_label: {}
  pervade_compliance_fields: {}
  pervade_title: {}
  show_children_on: {}
  compliant_answer: {}
  marking_info: {}
  hide_choices: {}
  allow_csv_text_fill: {}
  pervade_reverse_value: {}
  min_answers_amount: {}
  auto_answer_current_question_boolean: {}
  ranking: {}
  moreinfo: {}
  modified: {}
  created: {}

  auto_answer_choice_id:
    format: ID

  auto_answer_survey_question_choice_id:
    format: ID

  parent_2_id:
    format: ID

  auto_answer_survey_question_id:
    format: ID

  id:
    format: ID
    primary_key: true

  topic_id:
    format: ID

  related_check_id:
    format: ID

  auto_answer_current_question_choice_id:
    format: ID

  version_id:
    format: ID

  pervade_id:
    format: ID

  parent_id:
    format: ID

measures:
  count:
    aggregate_type: count

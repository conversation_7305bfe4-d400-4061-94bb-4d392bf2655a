# Reference this view as bigbrain__segment__sendgrid_deferred
schema: bigbrain
table_name: segment__sendgrid_deferred

dimensions:
  attempt: {}
  category: {}
  email: {}
  event: {}
  ip: {}
  loaded_at: {}
  received_at: {}
  response: {}
  timestamp: {}
  tls: {}
  uuid_ts: {}
  sg_template_name: {}
  cert_err: {}
  audience: {}
  environment: {}
  name: {}

  anymail_id:
    format: ID

  id:
    format: ID
    primary_key: true

  sg_event_id:
    format: ID

  sg_message_id:
    format: ID

  smtp_id:
    format: ID

  sg_template_id:
    format: ID

measures:
  count:
    aggregate_type: count

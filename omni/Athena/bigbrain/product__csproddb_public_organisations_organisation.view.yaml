# Reference this view as bigbrain__product__csproddb_public_organisations_organisation
label: Organisations

schema: bigbrain
table_name: product__csproddb_public_organisations_organisation

dimensions:
  showmodal: {}
  plans_ids: {}
  marketplace_tile_enabled: {}
  is_partner_org: {}
  ce_price: {}
  industry: {}
  superscript_reference: {}
  superscript_product_code: {}
  email_message: {}
  user_journey_passed: {}
  max_cap_users: {}
  cep_audit_request_date: {}
  pricing_band: {}
  trainings_tab_enabled: {}
  is_test: {}
  security_emails_frequency: {}
  legal_company_name: {}
  email: {}
  coupon_code: {}
  gdpr_price: {}
  license_price: {}
  disable_user_fix: {}
  simulate_installed_devices_count: {}
  enable_onboarding_email: {}
  type_of_customer: {}
  industry_description: {}
  data_privacy_support: {}
  external_uuid: {}
  legal_company_number: {}
  software_support: {}
  main_distributor_org: {}
  no_of_staff: {}
  company_relationship: {}
  bulk_install: {}
  size: {}
  phone: {}
  name: {}
  is_trial: {}
  modified: {}

  # temp during migration, see RVO-2638
  created:
    sql: created
    group_label: Date Created

  # remove once dashboards are migrated
  date_created:
    sql: created
    label: Date Created (use Created)
    group_label: Date Created

  partner_id:
    format: ID

  secure_id:
    format: ID

  id:
    format: ID
    primary_key: true

  partner_before_cancellation_id:
    format: ID

measures:
  count:
    aggregate_type: count

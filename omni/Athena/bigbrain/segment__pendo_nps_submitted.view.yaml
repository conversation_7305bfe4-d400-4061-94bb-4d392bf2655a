# Reference this view as bigbrain__segment__pendo_nps_submitted
schema: bigbrain
table_name: segment__pendo_nps_submitted

dimensions:
  context_app_name: {}
  context_app_platform: {}
  context_library_name: {}
  context_library_version: {}
  context_subscription_name: {}
  context_user_agent: {}
  event: {}
  event_text: {}
  guide_properties_created_at: {}
  guide_properties_created_by_user_username: {}
  guide_properties_last_updated_at: {}
  guide_properties_last_updated_by_user_username: {}
  guide_properties_name: {}
  guide_properties_steps: {}
  loaded_at: {}
  nps_rating: {}
  nps_source: {}
  original_timestamp: {}
  received_at: {}
  sent_at: {}
  timestamp: {}
  uuid_ts: {}
  nps_reason: {}

  context_app_id:
    format: ID

  context_group_id:
    format: ID

  context_subscription_id:
    format: ID

  guide_id:
    format: ID

  guide_properties_id:
    format: ID

  guide_step_id:
    format: ID

  id:
    format: ID
    primary_key: true

  unique_id:
    format: ID

  user_id:
    format: ID

measures:
  count:
    aggregate_type: count

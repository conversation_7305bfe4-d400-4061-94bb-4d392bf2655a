# Reference this view as bigbrain__segment__intercom_conversations
schema: bigbrain
table_name: segment__intercom_conversations

dimensions:
  assignee_type: {}
  created_at: {}
  loaded_at: {}
  message_author_type: {}
  message_body: {}
  message_subject: {}
  open: {}
  parts: {}
  read: {}
  received_at: {}
  tags: {}
  updated_at: {}
  uuid_ts: {}

  assignee_id:
    format: ID

  id:
    format: ID
    primary_key: true

  message_author_id:
    format: ID

  user_id:
    format: ID

measures:
  count:
    aggregate_type: count

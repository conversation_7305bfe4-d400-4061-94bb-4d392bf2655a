# Reference this view as bigbrain__segment__stripe_charges
schema: bigbrain
table_name: segment__stripe_charges

dimensions:
  amount: {}
  amount_refunded: {}
  captured: {}
  created: {}
  currency: {}
  description: {}
  failure_code: {}
  failure_message: {}
  fraud_details_stripe_report: {}
  loaded_at: {}
  metadata_customer_email: {}
  paid: {}
  receipt_email: {}
  receipt_number: {}
  received_at: {}
  refunded: {}
  statement_descriptor: {}
  status: {}
  uuid_ts: {}

  balance_transaction_id:
    format: ID

  card_id:
    format: ID

  customer_id:
    format: ID

  id:
    format: ID
    primary_key: true

  invoice_id:
    format: ID

  metadata_customer_id:
    format: ID

  metadata_order_id:
    format: ID

  payment_intent_id:
    format: ID

  payment_method_id:
    format: ID

measures:
  count:
    aggregate_type: count

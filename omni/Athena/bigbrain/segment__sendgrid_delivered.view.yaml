# Reference this view as bigbrain__segment__sendgrid_delivered
schema: bigbrain
table_name: segment__sendgrid_delivered

dimensions:
  category: {}
  email: {}
  event: {}
  ip: {}
  loaded_at: {}
  received_at: {}
  response: {}
  sg_template_name: {}
  timestamp: {}
  tls: {}
  uuid_ts: {}
  cert_err: {}
  mc_stats: {}
  audience: {}
  name: {}
  environment: {}

  anymail_id:
    format: ID

  id:
    format: ID
    primary_key: true

  sg_event_id:
    format: ID

  sg_message_id:
    format: ID

  sg_template_id:
    format: ID

  smtp_id:
    format: ID

  sendtest_id:
    format: ID

  template_id:
    format: ID

measures:
  count:
    aggregate_type: count

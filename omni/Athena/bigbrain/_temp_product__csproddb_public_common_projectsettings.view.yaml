# Reference this view as bigbrain___temp_product__csproddb_public_common_projectsettings
hidden: true

schema: bigbrain
table_name: _temp_product__csproddb_public_common_projectsettings

dimensions:
  auto_renewal_emails_distributor_partners: {}
  generic_response: {}
  vss_customer: {}
  uba_enabled: {}
  academy_enabled: {}
  superscript_report_password: {}
  certificate_auto_renewal_enabled: {}
  notifications_enabled: {}
  new_pages_tips: {}
  policy_guidance_html: {}
  cybersmart_learn_enablement: {}
  accounts_rewards_enabled: {}
  auto_renewal_emails_direct_customers: {}
  disable_direct_customer_signup: {}
  device_registration_blocklist: {}
  patch_enabled: {}
  web_notifications_config: {}
  smart_score_enabled: {}
  generic_versions: {}
  disable_payments: {}
  aviva_journey_coupons: {}
  auto_renewal_emails_distributor_partner_customers: {}
  auto_renewal_emails_direct_partners: {}
  vss_prospect: {}
  web_notifications_enabled: {}
  scheduled_installer_expiry_minutes: {}
  auto_renewal_emails_direct_partner_customers: {}
  record_api_requests: {}
  r_n_r_toolbox_enabled: {}
  tax_abbr: {}
  devices_cleaning_enabled: {}
  superscript_report_recipients: {}
  disable_certifications: {}
  blog_story: {}

  id:
    format: ID
    primary_key: true

measures:
  count:
    aggregate_type: count

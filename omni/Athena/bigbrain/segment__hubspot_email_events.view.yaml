# Reference this view as bigbrain__segment__hubspot_email_events
schema: bigbrain
table_name: segment__hubspot_email_events

dimensions:
  app_name: {}
  attempt: {}
  browser_family: {}
  browser_name: {}
  browser_producer: {}
  browser_producer_url: {}
  browser_type: {}
  browser_url: {}
  created: {}
  device_type: {}
  duration: {}
  filtered_event: {}
  from: {}
  loaded_at: {}
  location_city: {}
  location_country: {}
  location_latitude: {}
  location_state: {}
  portal_subscription_status: {}
  received_at: {}
  recipient: {}
  requested_by: {}
  response: {}
  sent_by_created: {}
  source: {}
  subject: {}
  subscriptions_0_legal_basis_change_legal_basis_explanation: {}
  subscriptions_0_legal_basis_change_legal_basis_type: {}
  subscriptions_0_legal_basis_change_opt_state: {}
  subscriptions_0_status: {}
  subscriptions_1_legal_basis_change_legal_basis_explanation: {}
  subscriptions_1_legal_basis_change_legal_basis_type: {}
  subscriptions_1_legal_basis_change_opt_state: {}
  subscriptions_1_status: {}
  subscriptions_2_legal_basis_change_legal_basis_explanation: {}
  subscriptions_2_legal_basis_change_legal_basis_type: {}
  subscriptions_2_legal_basis_change_opt_state: {}
  subscriptions_2_status: {}
  subscriptions_3_legal_basis_change_legal_basis_explanation: {}
  subscriptions_3_legal_basis_change_legal_basis_type: {}
  subscriptions_3_legal_basis_change_opt_state: {}
  subscriptions_3_status: {}
  subscriptions_4_legal_basis_change_legal_basis_explanation: {}
  subscriptions_4_legal_basis_change_legal_basis_type: {}
  subscriptions_4_legal_basis_change_opt_state: {}
  subscriptions_4_status: {}
  subscriptions_5_legal_basis_change_legal_basis_explanation: {}
  subscriptions_5_legal_basis_change_legal_basis_type: {}
  subscriptions_5_legal_basis_change_opt_state: {}
  subscriptions_5_status: {}
  type: {}
  user_agent: {}
  uuid_ts: {}
  drop_message: {}
  drop_reason: {}
  obsoleted_by_created: {}
  reply_to_0: {}
  caused_by_created: {}
  browser_version_0: {}
  category: {}
  status: {}
  subscriptions_6_legal_basis_change_legal_basis_explanation: {}
  subscriptions_6_legal_basis_change_legal_basis_type: {}
  subscriptions_6_legal_basis_change_opt_state: {}
  subscriptions_6_status: {}
  subscriptions_7_legal_basis_change_legal_basis_explanation: {}
  subscriptions_7_legal_basis_change_legal_basis_type: {}
  subscriptions_7_legal_basis_change_opt_state: {}
  subscriptions_7_status: {}
  url: {}
  subscriptions_8_legal_basis_change_legal_basis_explanation: {}
  subscriptions_8_legal_basis_change_legal_basis_type: {}
  subscriptions_8_legal_basis_change_opt_state: {}
  subscriptions_8_status: {}
  referer: {}
  location_longitude: {}
  location_zipcode: {}
  suppressed_message: {}
  suppressed_reason: {}

  app_id:
    format: ID

  email_campaign_id:
    format: ID

  id:
    format: ID
    primary_key: true

  portal_id:
    format: ID

  requested_by_user_id:
    format: ID

  sent_by_id:
    format: ID

  source_id:
    format: ID

  subscriptions_0_id:
    format: ID

  subscriptions_1_id:
    format: ID

  subscriptions_2_id:
    format: ID

  subscriptions_3_id:
    format: ID

  subscriptions_4_id:
    format: ID

  subscriptions_5_id:
    format: ID

  obsoleted_by_id:
    format: ID

  caused_by_id:
    format: ID

  link_id:
    format: ID

  smtp_id:
    format: ID

  subscriptions_6_id:
    format: ID

  subscriptions_7_id:
    format: ID

  subscriptions_8_id:
    format: ID

  email_campaign_group_id:
    format: ID

measures:
  count:
    aggregate_type: count

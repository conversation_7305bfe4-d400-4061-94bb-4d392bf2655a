# Reference this view as bigbrain__segment__aircall_call_transferred
schema: bigbrain
table_name: segment__aircall_call_transferred

dimensions:
  answered_at: {}
  archived: {}
  comments: {}
  context_library_name: {}
  context_library_version: {}
  direct_link: {}
  direction: {}
  duration: {}
  event: {}
  event_text: {}
  loaded_at: {}
  number_availability_status: {}
  number_country: {}
  number_digits: {}
  number_direct_link: {}
  number_is_ivr: {}
  number_live_recording_activated: {}
  number_messages_callback_later: {}
  number_messages_closed: {}
  number_messages_ivr: {}
  number_messages_voicemail: {}
  number_messages_waiting: {}
  number_messages_welcome: {}
  number_name: {}
  number_open: {}
  number_time_zone: {}
  original_timestamp: {}
  raw_digits: {}
  received_at: {}
  sent_at: {}
  started_at: {}
  status: {}
  tags: {}
  teams: {}
  timestamp: {}
  transferred_to_availability_status: {}
  transferred_to_available: {}
  transferred_to_direct_link: {}
  transferred_to_email: {}
  transferred_to_name: {}
  user_availability_status: {}
  user_available: {}
  user_direct_link: {}
  user_email: {}
  user_name: {}
  uuid_ts: {}
  call_uuid: {}
  cost: {}
  transferred_by_availability_status: {}
  transferred_by_available: {}
  transferred_by_direct_link: {}
  transferred_by_email: {}
  transferred_by_language: {}
  transferred_by_name: {}
  transferred_by_wrap_up_time: {}
  transferred_to_language: {}
  transferred_to_wrap_up_time: {}
  user_language: {}
  user_wrap_up_time: {}

  _id:
    format: ID
    hidden: true

  id:
    format: ID
    primary_key: true

  number_id:
    format: ID

  transferred_to_id:
    format: ID

  user_id:
    format: ID

  transferred_by_id:
    format: ID

measures:
  count:
    aggregate_type: count

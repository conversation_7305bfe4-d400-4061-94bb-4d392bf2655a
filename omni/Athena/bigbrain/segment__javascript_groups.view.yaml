# Reference this view as bigbrain__segment__javascript_groups
schema: bigbrain
table_name: segment__javascript_groups

dimensions:
  context_ip: {}
  context_library_name: {}
  context_library_version: {}
  context_page_path: {}
  context_page_referrer: {}
  context_page_title: {}
  context_page_url: {}
  context_user_agent: {}
  loaded_at: {}
  name: {}
  original_timestamp: {}
  received_at: {}
  sent_at: {}
  timestamp: {}
  uuid_ts: {}
  context_page_search: {}
  context_locale: {}
  context_campaign_medium: {}
  context_campaign_name: {}
  context_campaign_source: {}
  context_campaign_term: {}
  context_campaign_content: {}
  aviva_customer: {}
  bulk_install: {}
  ce_addon_date: {}
  ce_addon_purchased: {}
  ce_certified: {}
  ce_certified_date: {}
  ce_count_issued: {}
  ce_percentage_complete_questionnaire: {}
  cep_addon_date: {}
  cep_addon_purchased: {}
  cep_certified: {}
  cep_certified_date: {}
  cep_count_issued: {}
  chargebee: {}
  company_relationship: {}
  created_at: {}
  employees: {}
  gdpr_addon_date: {}
  gdpr_addon_purchased: {}
  gdpr_certified: {}
  gdpr_certified_date: {}
  gdpr_count_issued: {}
  gdpr_percentage_complete: {}
  has_app_plan: {}
  industry: {}
  legal_name: {}
  number_of_app_users: {}
  number_of_apps_installed: {}
  partner_name: {}
  policies_support: {}
  skip_payment: {}
  has_ce_and_gdpr_single_subscription: {}
  has_certification_annual_subscription: {}
  has_certification_ce_subscription: {}
  has_certification_gdpr_subscription: {}
  has_certification_monthly_subscription: {}
  has_certification_subscription: {}
  has_gdpr_annual_subscription: {}
  has_software_annual_subscription: {}
  has_software_monthly_subscription: {}
  has_software_pro_subscription: {}
  has_software_regular_subscription: {}
  has_software_subscription: {}
  has_software_subscription_bundle: {}
  is_billed_annual: {}
  is_billed_monthly: {}
  subscriptions_coupon_code: {}
  subscriptions_next_billing_at: {}
  subscriptions_status: {}
  distributor_name: {}
  subscriptions: {}

  anonymous_id:
    format: ID

  group_id:
    format: ID

  id:
    format: ID
    primary_key: true

  user_id:
    format: ID

  subscriptions_addon_id:
    format: ID

  subscriptions_plan_id:
    format: ID

measures:
  count:
    aggregate_type: count

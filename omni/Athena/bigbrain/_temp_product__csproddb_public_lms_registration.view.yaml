# Reference this view as bigbrain___temp_product__csproddb_public_lms_registration
hidden: true

schema: bigbrain
table_name: _temp_product__csproddb_public_lms_registration

dimensions:
  first_accessed_on: {}
  completed_on: {}
  completed: {}
  time_taken: {}
  last_accessed_on: {}
  score: {}
  passing_status: {}
  created: {}
  modified: {}

  course_id:
    format: ID

  registration_id:
    format: ID

  app_user_id:
    format: ID

  id:
    format: ID
    primary_key: true

measures:
  count:
    aggregate_type: count

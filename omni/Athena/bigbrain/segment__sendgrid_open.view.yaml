# Reference this view as bigbrain__segment__sendgrid_open
schema: bigbrain
table_name: segment__sendgrid_open

dimensions:
  category: {}
  email: {}
  event: {}
  ip: {}
  loaded_at: {}
  received_at: {}
  sg_content_type: {}
  sg_template_name: {}
  timestamp: {}
  useragent: {}
  uuid_ts: {}
  mc_stats: {}
  sg_machine_open: {}
  audience: {}
  name: {}
  environment: {}

  anymail_id:
    format: ID

  id:
    format: ID
    primary_key: true

  sg_event_id:
    format: ID

  sg_message_id:
    format: ID

  sg_template_id:
    format: ID

  smtp_id:
    format: ID

  sendtest_id:
    format: ID

  template_id:
    format: ID

measures:
  count:
    aggregate_type: count

# Reference this view as bigbrain__segment__adwords_campaign_performance_reports
schema: bigbrain
table_name: segment__adwords_campaign_performance_reports

dimensions:
  active_view_impressions: {}
  active_view_measurability: {}
  active_view_measurable_cost: {}
  active_view_measurable_impressions: {}
  active_view_viewability: {}
  advertising_channel_sub_type: {}
  all_conversion_rate: {}
  all_conversion_value: {}
  all_conversions: {}
  amount: {}
  average_cost: {}
  average_position: {}
  average_time_on_site: {}
  bounce_rate: {}
  campaign_status: {}
  campaign_trial_type: {}
  click_assisted_conversions: {}
  clicks: {}
  conversion_value: {}
  conversions: {}
  cost: {}
  date_start: {}
  date_stop: {}
  engagements: {}
  gmail_forwards: {}
  gmail_saves: {}
  gmail_secondary_clicks: {}
  impression_assisted_conversions: {}
  impression_reach: {}
  impressions: {}
  interaction_types: {}
  interactions: {}
  invalid_clicks: {}
  is_budget_explicitly_shared: {}
  loaded_at: {}
  received_at: {}
  uuid_ts: {}
  value_per_all_conversion: {}
  video_quartile_100_rate: {}
  video_quartile_25_rate: {}
  video_quartile_50_rate: {}
  video_quartile_75_rate: {}
  video_view_rate: {}
  video_views: {}
  view_through_conversions: {}

  adwords_customer_id:
    format: ID

  base_campaign_id:
    format: ID

  budget_id:
    format: ID

  campaign_id:
    format: ID

  id:
    format: ID
    primary_key: true

measures:
  count:
    aggregate_type: count

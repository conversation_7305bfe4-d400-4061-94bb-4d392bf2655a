# Reference this view as bigbrain___temp_product__csproddb_public_pg_stat_statements
hidden: true

schema: bigbrain
table_name: _temp_product__csproddb_public_pg_stat_statements

dimensions:
  total_exec_time: {}
  stddev_exec_time: {}
  shared_blks_hit: {}
  min_plan_time: {}
  userid: {}
  local_blks_hit: {}
  shared_blks_written: {}
  stddev_plan_time: {}
  min_exec_time: {}
  local_blks_read: {}
  temp_blks_read: {}
  shared_blks_dirtied: {}
  query: {}
  max_plan_time: {}
  wal_fpi: {}
  local_blks_written: {}
  blk_write_time: {}
  rows: {}
  local_blks_dirtied: {}
  blk_read_time: {}
  mean_plan_time: {}
  queryid: {}
  max_exec_time: {}
  wal_bytes: {}
  temp_blks_written: {}
  calls: {}
  plans: {}
  dbid: {}
  mean_exec_time: {}
  total_plan_time: {}
  shared_blks_read: {}
  wal_records: {}

measures:
  count:
    aggregate_type: count

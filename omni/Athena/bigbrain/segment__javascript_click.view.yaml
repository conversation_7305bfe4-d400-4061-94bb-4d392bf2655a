# Reference this view as bigbrain__segment__javascript_click
schema: bigbrain
table_name: segment__javascript_click

dimensions:
  click_classes: {}
  click_label: {}
  click_url: {}
  context_ip: {}
  context_library_name: {}
  context_library_version: {}
  context_locale: {}
  context_page_path: {}
  context_page_referrer: {}
  context_page_search: {}
  context_page_title: {}
  context_page_url: {}
  context_user_agent: {}
  event: {}
  event_text: {}
  loaded_at: {}
  original_timestamp: {}
  received_at: {}
  sent_at: {}
  timestamp: {}
  uuid_ts: {}
  context_campaign_medium: {}
  context_campaign_name: {}
  context_campaign_source: {}
  context_campaign_term: {}
  context_campaign_content: {}

  anonymous_id:
    format: ID

  click_id:
    format: ID

  id:
    format: ID
    primary_key: true

  user_id:
    format: ID

measures:
  count:
    aggregate_type: count

# Reference this view as bigbrain___temp_product__csproddb_public_rulebook_issuedcertification
hidden: true

schema: bigbrain
table_name: _temp_product__csproddb_public_rulebook_issuedcertification

dimensions:
  date: {}
  report_file: {}
  blockmark_evidence_of_insurance_forwarding_url: {}
  blockmark_certificate_forwarding_url: {}
  insurance_file: {}
  insurance_number: {}
  custom_cert_sent: {}
  blockmark_certificate_registration_url: {}
  evidence_of_insurance_file: {}
  number: {}
  certificate_file: {}
  custom_cert_date: {}
  blockmark_evidence_of_insurance_registration_url: {}
  created: {}
  modified: {}

  assessor_id:
    format: ID

  certificate_id:
    format: ID

  id:
    format: ID
    primary_key: true

measures:
  count:
    aggregate_type: count

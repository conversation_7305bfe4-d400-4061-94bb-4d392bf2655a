# Reference this view as bigbrain__xero__invoices
schema: bigbrain
table_name: xero__invoices

dimensions:
  amount_credited: {}
  amount_due: {}
  amount_paid: {}
  contact_code: {}
  contact_email_address: {}
  contact_first_name: {}
  contact_last_name: {}
  contact_name: {}
  currency_code: {}
  currency_rate: {}
  date: {}
  due_date: {}
  expected_payment_date: {}
  fully_paid_on_date: {}
  has_attachments: {}
  has_errors: {}
  invoice_number: {}
  is_discounted: {}
  planned_payment_date: {}
  reference: {}
  sent_to_contact: {}
  status: {}
  sub_total: {}
  total: {}
  total_discount: {}
  total_tax: {}
  type: {}
  updated_date_utc: {}
  url: {}

  branding_theme_id:
    format: ID

  contact_id:
    format: ID

  invoice_id:
    format: ID

  repeating_invoice_id:
    format: ID

measures:
  count:
    aggregate_type: count

# Reference this view as bigbrain___temp_product__csproddb_public_opswat_patch_productinstaller
hidden: true

schema: bigbrain
table_name: _temp_product__csproddb_public_opswat_patch_productinstaller

dimensions:
  minimum_version: {}
  sha256: {}
  expected_sha1: {}
  eula: {}
  language: {}
  title: {}
  path: {}
  file_type: {}
  architecture: {}
  severity: {}
  expected_sha512: {}
  index: {}
  url: {}
  expected_md5: {}
  sha1: {}
  release_note: {}
  size: {}
  certificates: {}
  release_date: {}
  category: {}
  md5: {}
  expected_sha256: {}

  os_id:
    format: ID

  analog_id:
    format: ID

  product_id:
    format: ID

  id:
    format: ID
    primary_key: true

  patch_id:
    format: ID

  type_id:
    format: ID

  security_update_id:
    format: ID

measures:
  count:
    aggregate_type: count

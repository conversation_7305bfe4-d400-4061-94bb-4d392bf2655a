# Reference this view as bigbrain__segment__hubspot_email_subscription_events
schema: bigbrain
table_name: segment__hubspot_email_subscription_events

dimensions:
  changes_0_caused_by_event_created: {}
  changes_0_change: {}
  changes_0_change_type: {}
  changes_0_source: {}
  changes_0_timestamp: {}
  changes_1_caused_by_event_created: {}
  changes_1_change: {}
  changes_1_change_type: {}
  changes_1_source: {}
  changes_1_timestamp: {}
  changes_2_caused_by_event_created: {}
  changes_2_change: {}
  changes_2_change_type: {}
  changes_2_source: {}
  changes_2_timestamp: {}
  changes_3_caused_by_event_created: {}
  changes_3_change: {}
  changes_3_change_type: {}
  changes_3_source: {}
  changes_3_timestamp: {}
  changes_4_caused_by_event_created: {}
  changes_4_change: {}
  changes_4_change_type: {}
  changes_4_source: {}
  changes_4_timestamp: {}
  changes_5_caused_by_event_created: {}
  changes_5_change: {}
  changes_5_change_type: {}
  changes_5_source: {}
  changes_5_timestamp: {}
  changes_6_caused_by_event_created: {}
  changes_6_change: {}
  changes_6_change_type: {}
  changes_6_source: {}
  changes_6_timestamp: {}
  loaded_at: {}
  received_at: {}
  recipient: {}
  timestamp: {}
  uuid_ts: {}
  changes_7_caused_by_event_created: {}
  changes_7_change: {}
  changes_7_change_type: {}
  changes_7_source: {}
  changes_7_timestamp: {}
  changes_8_caused_by_event_created: {}
  changes_8_change: {}
  changes_8_change_type: {}
  changes_8_source: {}
  changes_8_timestamp: {}
  changes_9_caused_by_event_created: {}
  changes_9_change: {}
  changes_9_change_type: {}
  changes_9_source: {}
  changes_9_timestamp: {}

  changes_0_caused_by_event_id:
    format: ID

  changes_0_email_subscription_event_id:
    format: ID

  changes_0_id:
    format: ID

  changes_0_portal_id:
    format: ID

  changes_0_subscription_id:
    format: ID

  changes_1_caused_by_event_id:
    format: ID

  changes_1_email_subscription_event_id:
    format: ID

  changes_1_id:
    format: ID

  changes_1_portal_id:
    format: ID

  changes_1_subscription_id:
    format: ID

  changes_2_caused_by_event_id:
    format: ID

  changes_2_email_subscription_event_id:
    format: ID

  changes_2_id:
    format: ID

  changes_2_portal_id:
    format: ID

  changes_2_subscription_id:
    format: ID

  changes_3_caused_by_event_id:
    format: ID

  changes_3_email_subscription_event_id:
    format: ID

  changes_3_id:
    format: ID

  changes_3_portal_id:
    format: ID

  changes_3_subscription_id:
    format: ID

  changes_4_caused_by_event_id:
    format: ID

  changes_4_email_subscription_event_id:
    format: ID

  changes_4_id:
    format: ID

  changes_4_portal_id:
    format: ID

  changes_4_subscription_id:
    format: ID

  changes_5_caused_by_event_id:
    format: ID

  changes_5_email_subscription_event_id:
    format: ID

  changes_5_id:
    format: ID

  changes_5_portal_id:
    format: ID

  changes_5_subscription_id:
    format: ID

  changes_6_caused_by_event_id:
    format: ID

  changes_6_email_subscription_event_id:
    format: ID

  changes_6_id:
    format: ID

  changes_6_portal_id:
    format: ID

  id:
    format: ID
    primary_key: true

  normalized_email_id:
    format: ID

  portal_id:
    format: ID

  changes_6_subscription_id:
    format: ID

  changes_7_caused_by_event_id:
    format: ID

  changes_7_email_subscription_event_id:
    format: ID

  changes_7_id:
    format: ID

  changes_7_portal_id:
    format: ID

  changes_7_subscription_id:
    format: ID

  changes_8_caused_by_event_id:
    format: ID

  changes_8_email_subscription_event_id:
    format: ID

  changes_8_id:
    format: ID

  changes_8_portal_id:
    format: ID

  changes_8_subscription_id:
    format: ID

  changes_9_caused_by_event_id:
    format: ID

  changes_9_email_subscription_event_id:
    format: ID

  changes_9_id:
    format: ID

  changes_9_portal_id:
    format: ID

measures:
  count:
    aggregate_type: count

# Reference this view as bigbrain__segment__adwords_ad_performance_reports
schema: bigbrain
table_name: segment__adwords_ad_performance_reports

dimensions:
  account_currency_code: {}
  account_descriptive_name: {}
  active_view_impressions: {}
  active_view_measurability: {}
  active_view_measurable_cost: {}
  active_view_measurable_impressions: {}
  active_view_viewability: {}
  all_conversion_rate: {}
  all_conversion_value: {}
  all_conversions: {}
  average_cost: {}
  average_position: {}
  average_time_on_site: {}
  bounce_rate: {}
  click_assisted_conversions: {}
  clicks: {}
  conversion_value: {}
  conversions: {}
  cost: {}
  date_start: {}
  date_stop: {}
  engagements: {}
  gmail_forwards: {}
  gmail_saves: {}
  gmail_secondary_clicks: {}
  impression_assisted_conversions: {}
  impressions: {}
  interaction_types: {}
  interactions: {}
  loaded_at: {}
  received_at: {}
  uuid_ts: {}
  value_per_all_conversion: {}
  video_quartile_100_rate: {}
  video_quartile_25_rate: {}
  video_quartile_50_rate: {}
  video_quartile_75_rate: {}
  video_view_rate: {}
  video_views: {}
  view_through_conversions: {}

  ad_id:
    format: ID

  adwords_customer_id:
    format: ID

  id:
    format: ID
    primary_key: true

  ad_group_id:
    format: ID

measures:
  count:
    aggregate_type: count

# Reference this view as bigbrain__segment__hubspot_email_subscriptions
schema: bigbrain
table_name: segment__hubspot_email_subscriptions

dimensions:
  bounced: {}
  email: {}
  loaded_at: {}
  marked_as_spam: {}
  portal_subscription_legal_basis: {}
  portal_subscription_legal_basis_explanation: {}
  received_at: {}
  status: {}
  subscribed: {}
  subscription_statuses_0_legal_basis: {}
  subscription_statuses_0_legal_basis_explanation: {}
  subscription_statuses_0_opt_state: {}
  subscription_statuses_0_subscribed: {}
  subscription_statuses_0_updated_at: {}
  subscription_statuses_1_legal_basis: {}
  subscription_statuses_1_legal_basis_explanation: {}
  subscription_statuses_1_opt_state: {}
  subscription_statuses_1_subscribed: {}
  subscription_statuses_1_updated_at: {}
  subscription_statuses_2_legal_basis: {}
  subscription_statuses_2_legal_basis_explanation: {}
  subscription_statuses_2_opt_state: {}
  subscription_statuses_2_subscribed: {}
  subscription_statuses_2_updated_at: {}
  subscription_statuses_3_legal_basis: {}
  subscription_statuses_3_legal_basis_explanation: {}
  subscription_statuses_3_opt_state: {}
  subscription_statuses_3_subscribed: {}
  subscription_statuses_3_updated_at: {}
  subscription_statuses_4_legal_basis: {}
  subscription_statuses_4_legal_basis_explanation: {}
  subscription_statuses_4_opt_state: {}
  subscription_statuses_4_subscribed: {}
  subscription_statuses_4_updated_at: {}
  subscription_statuses_5_legal_basis: {}
  subscription_statuses_5_legal_basis_explanation: {}
  subscription_statuses_5_opt_state: {}
  subscription_statuses_5_subscribed: {}
  subscription_statuses_5_updated_at: {}
  subscription_statuses_6_legal_basis: {}
  subscription_statuses_6_legal_basis_explanation: {}
  subscription_statuses_6_opt_state: {}
  subscription_statuses_6_subscribed: {}
  subscription_statuses_6_updated_at: {}
  unsubscribe_from_portal: {}
  uuid_ts: {}
  subscription_statuses_7_legal_basis: {}
  subscription_statuses_7_legal_basis_explanation: {}
  subscription_statuses_7_opt_state: {}
  subscription_statuses_7_subscribed: {}
  subscription_statuses_7_updated_at: {}
  subscription_statuses_8_legal_basis: {}
  subscription_statuses_8_legal_basis_explanation: {}
  subscription_statuses_8_opt_state: {}
  subscription_statuses_8_subscribed: {}
  subscription_statuses_8_updated_at: {}

  id:
    format: ID
    primary_key: true

  portal_id:
    format: ID

  subscription_statuses_0_id:
    format: ID

  subscription_statuses_1_id:
    format: ID

  subscription_statuses_2_id:
    format: ID

  subscription_statuses_3_id:
    format: ID

  subscription_statuses_4_id:
    format: ID

  subscription_statuses_5_id:
    format: ID

  subscription_statuses_6_id:
    format: ID

  subscription_statuses_7_id:
    format: ID

  subscription_statuses_8_id:
    format: ID

measures:
  count:
    aggregate_type: count

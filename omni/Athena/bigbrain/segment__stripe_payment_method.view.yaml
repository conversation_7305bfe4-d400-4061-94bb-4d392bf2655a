# Reference this view as bigbrain__segment__stripe_payment_method
schema: bigbrain
table_name: segment__stripe_payment_method

dimensions:
  billing_details_address_city: {}
  billing_details_address_country: {}
  billing_details_address_line1: {}
  billing_details_address_line2: {}
  billing_details_address_postal_code: {}
  billing_details_address_state: {}
  billing_details_email: {}
  billing_details_name: {}
  card_brand: {}
  card_checks_address_line1_check: {}
  card_checks_address_postal_code_check: {}
  card_checks_cvc_check: {}
  card_country: {}
  card_exp_month: {}
  card_exp_year: {}
  card_fingerprint: {}
  card_funding: {}
  card_last4: {}
  card_networks_available: {}
  card_three_d_secure_usage_supported: {}
  created: {}
  livemode: {}
  loaded_at: {}
  received_at: {}
  type: {}
  uuid_ts: {}

  customer_id:
    format: ID

  id:
    format: ID
    primary_key: true

measures:
  count:
    aggregate_type: count

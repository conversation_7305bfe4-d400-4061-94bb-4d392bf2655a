# Reference this view as bigbrain__segment__javascript_identifies
schema: bigbrain
table_name: segment__javascript_identifies

dimensions:
  context_ip: {}
  context_library_name: {}
  context_library_version: {}
  context_page_path: {}
  context_page_title: {}
  context_page_url: {}
  context_user_agent: {}
  email: {}
  loaded_at: {}
  name: {}
  original_timestamp: {}
  received_at: {}
  sent_at: {}
  timestamp: {}
  uuid_ts: {}
  context_page_referrer: {}
  created_at: {}
  context_page_search: {}
  context_locale: {}
  context_campaign_medium: {}
  context_campaign_name: {}
  context_campaign_source: {}
  context_campaign_term: {}
  context_campaign_content: {}
  contact_relationship: {}
  distributor_user: {}
  first_name: {}
  last_name: {}
  partner_user: {}

  anonymous_id:
    format: ID

  id:
    format: ID
    primary_key: true

  user_id:
    format: ID

measures:
  count:
    aggregate_type: count

# Reference this view as bigbrain__segment__intercom_users
schema: bigbrain
table_name: segment__intercom_users

dimensions:
  anonymous: {}
  avatar_image_url: {}
  companies: {}
  created_at: {}
  custom_distributor_user: {}
  custom_has_partner: {}
  custom_job_title: {}
  custom_last_wootric_nps_comment: {}
  custom_last_wootric_nps_response_at: {}
  custom_last_wootric_nps_score: {}
  custom_lead_source: {}
  custom_mobile_attempted_email: {}
  custom_partner_user: {}
  custom_salesforce_object_type: {}
  custom_salesforce_owner_email: {}
  custom_salesforce_owner_name: {}
  custom_salesforce_status: {}
  custom_salesforce_title: {}
  custom_status: {}
  custom_stripe_account_balance: {}
  custom_stripe_card_brand: {}
  custom_stripe_card_expires_at: {}
  custom_stripe_deleted: {}
  custom_stripe_delinquent: {}
  custom_stripe_last_charge_amount: {}
  custom_stripe_last_charge_at: {}
  custom_stripe_plan: {}
  custom_stripe_plan_interval: {}
  custom_stripe_plan_price: {}
  custom_stripe_subscription_period_start_at: {}
  custom_stripe_subscription_status: {}
  custom_tag: {}
  email: {}
  last_request_at: {}
  loaded_at: {}
  location_city_name: {}
  location_continent_code: {}
  location_country_code: {}
  location_country_name: {}
  location_latitude: {}
  location_longitude: {}
  location_postal_code: {}
  location_region_name: {}
  location_timezone: {}
  name: {}
  pseudonym: {}
  received_at: {}
  remote_created_at: {}
  segments: {}
  session_count: {}
  signed_up_at: {}
  tags: {}
  unsubscribed_from_emails: {}
  updated_at: {}
  user_agent_data: {}
  uuid_ts: {}
  custom_company_size: {}
  custom_trait1: {}
  custom_trait2: {}
  custom_trait3: {}
  custom_hubspot_tracking_cookie: {}
  custom_contact_relationship: {}
  custom_cb_card_expired_at: {}
  custom_cb_company_name: {}
  custom_cb_cust_autocollect: {}
  custom_cb_cust_mrr: {}
  custom_cb_cust_status: {}
  custom_cb_cust_unpaid_amt: {}
  custom_cb_customer_signed_up_at: {}
  custom_cb_first_rec_inv_amt: {}
  custom_cb_first_rec_inv_at: {}
  custom_cb_first_rec_inv_cur: {}
  custom_cb_latest_rec_inv_amt: {}
  custom_cb_latest_rec_inv_at: {}
  custom_cb_latest_rec_inv_cur: {}
  custom_cb_payment_type: {}
  custom_cb_sub_activated_at: {}
  custom_cb_sub_cancelled_at: {}
  custom_cb_sub_created_at: {}
  custom_cb_sub_cur_term_end_at: {}
  custom_cb_sub_cur_term_start_at: {}
  custom_cb_sub_nextbill_at: {}
  custom_cb_sub_plan_currency: {}
  custom_cb_sub_plan_interval: {}
  custom_cb_sub_plan_name: {}
  custom_cb_sub_plan_price: {}
  custom_cb_sub_plan_quantity: {}
  custom_cb_sub_status: {}
  custom_last_recording: {}

  custom_article_id:
    format: ID

  custom_device_id:
    format: ID

  custom_device_model_id:
    format: ID

  custom_salesforce_id:
    format: ID

  custom_salesforce_owner_id:
    format: ID

  custom_stripe_id:
    format: ID

  id:
    format: ID
    primary_key: true

  user_id:
    format: ID

  custom_id:
    format: ID

measures:
  count:
    aggregate_type: count

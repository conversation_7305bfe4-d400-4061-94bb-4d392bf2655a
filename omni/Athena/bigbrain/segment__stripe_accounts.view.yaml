# Reference this view as bigbrain__segment__stripe_accounts
schema: bigbrain
table_name: segment__stripe_accounts

dimensions:
  business_logo: {}
  business_name: {}
  business_url: {}
  charges_enabled: {}
  country: {}
  default_currency: {}
  details_submitted: {}
  display_name: {}
  email: {}
  loaded_at: {}
  managed: {}
  received_at: {}
  statement_descriptor: {}
  support_address_city: {}
  support_address_country: {}
  support_address_line1: {}
  support_address_postal_code: {}
  support_email: {}
  support_phone: {}
  support_url: {}
  timezone: {}
  transfers_enabled: {}
  uuid_ts: {}

  id:
    format: ID
    primary_key: true

measures:
  count:
    aggregate_type: count

# Reference this view as bigbrain__product__csproddb_public_appusers_appreport
label: App Reports

schema: bigbrain
table_name: product__csproddb_public_appusers_appreport

dimensions:
  app_version: {}
  total_commands: {}
  total_passed_responses: {}
  total_passed: {}
  total_manual_fix: {}
  pass_percentage: {}
  domain: {}
  total_failed_responses: {}
  total_responses: {}
  total_manual_fix_left: {}
  total_failed: {}
  username: {}
  modified: {}

  # temp during migration, see RVO-2638
  created:
    sql: created
    label: Last Check In
    group_label: Date Created

  # remove once dashboards are migrated
  date_created:
    sql: created
    label: Check In (use Last Check In)
    group_label: Date Created

  app_install_id:
    format: ID

  id:
    format: ID
    primary_key: true

measures:
  count:
    aggregate_type: count

# Reference this view as bigbrain__segment__stripe_disputes
schema: bigbrain
table_name: segment__stripe_disputes

dimensions:
  amount: {}
  created: {}
  currency: {}
  evidence_customer_email_address: {}
  evidence_customer_name: {}
  evidence_customer_purchase_ip: {}
  evidence_details_due_by: {}
  evidence_details_has_evidence: {}
  evidence_details_past_due: {}
  evidence_details_submission_count: {}
  evidence_receipt: {}
  is_charge_refundable: {}
  loaded_at: {}
  reason: {}
  received_at: {}
  status: {}
  uuid_ts: {}

  charge_id:
    format: ID

  id:
    format: ID
    primary_key: true

measures:
  count:
    aggregate_type: count

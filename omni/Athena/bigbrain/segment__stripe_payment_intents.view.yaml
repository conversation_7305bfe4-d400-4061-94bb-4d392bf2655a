# Reference this view as bigbrain__segment__stripe_payment_intents
schema: bigbrain
table_name: segment__stripe_payment_intents

dimensions:
  amount: {}
  amount_capturable: {}
  amount_received: {}
  canceled_at: {}
  cancellation_reason: {}
  capture_method: {}
  confirmation_method: {}
  created: {}
  currency: {}
  description: {}
  livemode: {}
  loaded_at: {}
  metadata_customer_email: {}
  received_at: {}
  statement_descriptor: {}
  status: {}
  uuid_ts: {}
  receipt_email: {}

  application_id:
    format: ID

  customer_id:
    format: ID

  id:
    format: ID
    primary_key: true

  invoice_id:
    format: ID

  metadata_customer_id:
    format: ID

  metadata_order_id:
    format: ID

  payment_method_id:
    format: ID

measures:
  count:
    aggregate_type: count

# Reference this view as bigbrain__segment__aircall_call_archived
schema: bigbrain
table_name: segment__aircall_call_archived

dimensions:
  archived: {}
  asset: {}
  comments: {}
  context_library_name: {}
  context_library_version: {}
  direct_link: {}
  direction: {}
  duration: {}
  ended_at: {}
  event: {}
  event_text: {}
  loaded_at: {}
  missed_call_reason: {}
  number_availability_status: {}
  number_country: {}
  number_digits: {}
  number_direct_link: {}
  number_is_ivr: {}
  number_live_recording_activated: {}
  number_messages_callback_later: {}
  number_messages_closed: {}
  number_messages_ivr: {}
  number_messages_voicemail: {}
  number_messages_waiting: {}
  number_messages_welcome: {}
  number_name: {}
  number_open: {}
  number_time_zone: {}
  original_timestamp: {}
  raw_digits: {}
  received_at: {}
  sent_at: {}
  started_at: {}
  status: {}
  tags: {}
  teams: {}
  timestamp: {}
  uuid_ts: {}
  voicemail: {}
  answered_at: {}
  assigned_to_availability_status: {}
  assigned_to_available: {}
  assigned_to_direct_link: {}
  assigned_to_email: {}
  assigned_to_name: {}
  user_availability_status: {}
  user_available: {}
  user_direct_link: {}
  user_email: {}
  user_name: {}
  cost: {}
  contact_company_name: {}
  contact_created_at: {}
  contact_direct_link: {}
  contact_emails: {}
  contact_first_name: {}
  contact_information: {}
  contact_is_shared: {}
  contact_last_name: {}
  contact_phone_numbers: {}
  contact_updated_at: {}
  hangup_cause: {}
  assigned_by_availability_status: {}
  assigned_by_available: {}
  assigned_by_direct_link: {}
  assigned_by_email: {}
  assigned_by_language: {}
  assigned_by_name: {}
  assigned_by_wrap_up_time: {}
  assigned_to_language: {}
  assigned_to_wrap_up_time: {}
  user_language: {}
  user_wrap_up_time: {}
  call_uuid: {}

  _id:
    format: ID
    hidden: true

  id:
    format: ID
    primary_key: true

  number_id:
    format: ID

  user_id:
    format: ID

  assigned_to_id:
    format: ID

  contact_id:
    format: ID

  assigned_by_id:
    format: ID

measures:
  count:
    aggregate_type: count

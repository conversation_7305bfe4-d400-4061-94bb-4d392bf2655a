# Reference this view as bigbrain__segment__javascript_pages
schema: bigbrain
table_name: segment__javascript_pages

dimensions:
  context_campaign_medium: {}
  context_campaign_source: {}
  context_campaign_term: {}
  context_ip: {}
  context_library_name: {}
  context_library_version: {}
  context_page_path: {}
  context_page_referrer: {}
  context_page_search: {}
  context_page_title: {}
  context_page_url: {}
  context_user_agent: {}
  loaded_at: {}
  original_timestamp: {}
  path: {}
  received_at: {}
  referrer: {}
  search: {}
  sent_at: {}
  timestamp: {}
  title: {}
  url: {}
  uuid_ts: {}
  context_campaign_content: {}
  context_campaign_name: {}
  context_locale: {}
  context_campaign_licid: {}
  context_campaign_scoshurce: {}
  context_campaign_tewhat_20do_20yu_20need_20to_20do_20to_20get_20cyber_20essentialsrm: {}
  context_campaign_placement: {}
  context_campaign_utm_medium: {}
  context_campaign_utm_source: {}
  context_campaign_utm_campaign: {}
  context_campaign_utm_term: {}
  context_campaign_rsmart_co_uk_3futm_source: {}
  integrations_pendo: {}
  context_campaign_licrid: {}
  context_campaign_amp_utm_medium: {}
  context_campaign_amp_utm_source: {}

  anonymous_id:
    format: ID

  id:
    format: ID
    primary_key: true

  user_id:
    format: ID

  context_campaign_id:
    format: ID

measures:
  count:
    aggregate_type: count

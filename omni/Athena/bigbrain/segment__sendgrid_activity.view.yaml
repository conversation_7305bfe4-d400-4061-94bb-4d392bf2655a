# Reference this view as bigbrain__segment__sendgrid_activity
schema: bigbrain
table_name: segment__sendgrid_activity

dimensions:
  attempt: {}
  category: {}
  email: {}
  event: {}
  ip: {}
  loaded_at: {}
  reason: {}
  received_at: {}
  response: {}
  sg_content_type: {}
  sg_template_name: {}
  status: {}
  timestamp: {}
  tls: {}
  type: {}
  url: {}
  url_offset_index: {}
  url_offset_type: {}
  useragent: {}
  uuid_ts: {}
  cert_err: {}
  message: {}
  mc_stats: {}
  send_at: {}
  sg_machine_open: {}
  bounce_classification: {}
  audience: {}
  name: {}
  environment: {}
  date: {}

  anymail_id:
    format: ID

  asm_group_id:
    format: ID

  id:
    format: ID
    primary_key: true

  sg_event_id:
    format: ID

  sg_message_id:
    format: ID

  sg_template_id:
    format: ID

  smtp_id:
    format: ID

  sendtest_id:
    format: ID

  template_id:
    format: ID

measures:
  count:
    aggregate_type: count

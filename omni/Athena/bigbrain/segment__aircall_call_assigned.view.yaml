# Reference this view as bigbrain__segment__aircall_call_assigned
schema: bigbrain
table_name: segment__aircall_call_assigned

dimensions:
  answered_at: {}
  archived: {}
  assigned_to_availability_status: {}
  assigned_to_available: {}
  assigned_to_direct_link: {}
  assigned_to_email: {}
  assigned_to_name: {}
  comments: {}
  context_library_name: {}
  context_library_version: {}
  direct_link: {}
  direction: {}
  duration: {}
  ended_at: {}
  event: {}
  event_text: {}
  loaded_at: {}
  number_availability_status: {}
  number_country: {}
  number_digits: {}
  number_direct_link: {}
  number_is_ivr: {}
  number_live_recording_activated: {}
  number_messages_callback_later: {}
  number_messages_closed: {}
  number_messages_ivr: {}
  number_messages_voicemail: {}
  number_messages_waiting: {}
  number_messages_welcome: {}
  number_name: {}
  number_open: {}
  number_time_zone: {}
  original_timestamp: {}
  raw_digits: {}
  received_at: {}
  sent_at: {}
  started_at: {}
  status: {}
  tags: {}
  teams: {}
  timestamp: {}
  user_availability_status: {}
  user_available: {}
  user_direct_link: {}
  user_email: {}
  user_name: {}
  uuid_ts: {}
  assigned_by_availability_status: {}
  assigned_by_available: {}
  assigned_by_direct_link: {}
  assigned_by_email: {}
  assigned_by_language: {}
  assigned_by_name: {}
  assigned_by_wrap_up_time: {}
  assigned_to_language: {}
  assigned_to_wrap_up_time: {}
  cost: {}
  user_language: {}
  user_wrap_up_time: {}
  asset: {}
  missed_call_reason: {}
  voicemail: {}

  _id:
    format: ID
    hidden: true

  assigned_to_id:
    format: ID

  id:
    format: ID
    primary_key: true

  number_id:
    format: ID

  user_id:
    format: ID

  assigned_by_id:
    format: ID

measures:
  count:
    aggregate_type: count

# Reference this view as bigbrain__segment__stripe_cards
schema: bigbrain
table_name: segment__stripe_cards

dimensions:
  address_city: {}
  address_country: {}
  address_line1: {}
  address_line1_check: {}
  address_line2: {}
  address_state: {}
  address_zip: {}
  address_zip_check: {}
  brand: {}
  country: {}
  cvc_check: {}
  exp_month: {}
  exp_year: {}
  fingerprint: {}
  funding: {}
  last4: {}
  loaded_at: {}
  name: {}
  received_at: {}
  uuid_ts: {}
  is_deleted: {}

  customer_id:
    format: ID

  id:
    format: ID
    primary_key: true

measures:
  count:
    aggregate_type: count

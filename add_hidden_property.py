#!/usr/bin/env python3
"""
Script to add 'hidden: true' property to YAML view files with specific naming patterns.
This script processes files starting with '_temp_product__' and '_product__' to hide them
from the interface, while keeping 'product__' files visible.
"""

import os
import re
import sys

def should_process_file(filepath):
    """Check if file should be processed based on naming pattern."""
    filename = os.path.basename(filepath)
    return (filename.startswith('_temp_product__') or filename.startswith('_product__')) and filename.endswith('.view.yaml')

def has_hidden_property(content):
    """Check if the file already has 'hidden: true' property."""
    return re.search(r'^hidden:\s*true\s*$', content, re.MULTILINE) is not None

def add_hidden_property(content):
    """Add 'hidden: true' property after the reference comment and before schema line."""
    lines = content.split('\n')

    # Find the reference comment line and schema line
    reference_line_idx = -1
    schema_line_idx = -1

    for i, line in enumerate(lines):
        if line.startswith('# Reference this view as'):
            reference_line_idx = i
        elif line.startswith('schema:'):
            schema_line_idx = i
            break

    if reference_line_idx == -1 or schema_line_idx == -1:
        print(f"Warning: Could not find reference comment or schema line")
        return content

    # Insert 'hidden: true' immediately after reference comment
    insert_position = reference_line_idx + 1

    # Insert the hidden property
    lines.insert(insert_position, 'hidden: true')

    # Add an empty line after hidden property if the next line is not empty
    if insert_position + 1 < len(lines) and lines[insert_position + 1].strip() != '':
        lines.insert(insert_position + 1, '')

    return '\n'.join(lines)

def process_file(filepath):
    """Process a single YAML file to add hidden property."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if already has hidden property
        if has_hidden_property(content):
            return False, "Already has hidden property"
        
        # Add hidden property
        modified_content = add_hidden_property(content)
        
        # Write back to file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        return True, "Added hidden property"
        
    except Exception as e:
        return False, f"Error: {e}"

def scan_directory(directory):
    """Scan directory for files matching the patterns."""
    target_files = []
    
    for root, _, files in os.walk(directory):
        for file in files:
            filepath = os.path.join(root, file)
            if should_process_file(filepath):
                target_files.append(filepath)
    
    return sorted(target_files)

def main():
    """Main function to process all matching files."""
    
    # Directory to scan
    target_directory = "omni/Athena/bigbrain/"
    
    if not os.path.exists(target_directory):
        print(f"Error: Directory {target_directory} does not exist")
        sys.exit(1)
    
    # Find all files to process
    files_to_process = scan_directory(target_directory)
    
    print(f"Found {len(files_to_process)} files to process...")
    print()
    
    # Process each file
    processed_count = 0
    skipped_count = 0
    error_count = 0
    
    for filepath in files_to_process:
        success, message = process_file(filepath)
        
        if success:
            print(f"✓ Processed: {filepath}")
            processed_count += 1
        elif "Already has hidden property" in message:
            print(f"- Skipped: {filepath} ({message})")
            skipped_count += 1
        else:
            print(f"✗ Error: {filepath} ({message})")
            error_count += 1
    
    print()
    print(f"Summary:")
    print(f"  Processed: {processed_count} files")
    print(f"  Skipped: {skipped_count} files")
    print(f"  Errors: {error_count} files")
    print(f"  Total: {len(files_to_process)} files")

if __name__ == "__main__":
    main()
